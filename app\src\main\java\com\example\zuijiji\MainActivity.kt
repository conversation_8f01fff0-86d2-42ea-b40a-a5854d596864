package com.example.zuijiji

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.graphics.RenderEffect
import androidx.compose.ui.graphics.Shader
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.graphics.asComposeRenderEffect
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.foundation.Canvas
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.nativeCanvas
import kotlin.math.abs
import kotlin.math.sqrt
import kotlin.math.sin
import kotlin.math.cos
import kotlin.math.PI
import kotlin.math.pow
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.graphics.Shadow
import kotlin.math.roundToInt
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import com.example.zuijiji.ui.theme.ZuijijiTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            ZuijijiTheme {
                MainScreen()
            }
        }
    }
}

@Composable
fun MainScreen() {
    var backgroundImageUri by remember { mutableStateOf<Uri?>(null) }
    var showBlurDialog by remember { mutableStateOf(false) }
    var dialogOffset by remember { mutableStateOf(Offset.Zero) }
    var showMagnifier by remember { mutableStateOf(false) }

    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        backgroundImageUri = uri
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 背景内容层
        BackgroundLayer(
            backgroundImageUri = backgroundImageUri,
            imagePickerLauncher = imagePickerLauncher,
            onShowDialog = { showBlurDialog = true },
            onToggleMagnifier = { showMagnifier = !showMagnifier },
            showMagnifier = showMagnifier,
            dialogOffset = dialogOffset,
            showDialog = showBlurDialog
        )

        // 毛玻璃弹窗
        if (showBlurDialog) {
            RealTimeBlurDialog(
                backgroundImageUri = backgroundImageUri,
                onDismiss = { showBlurDialog = false },
                onOffsetChange = { dialogOffset = it }
            )
        }

        // 放大镜
        if (showMagnifier && backgroundImageUri != null) {
            MagnifierGlass(
                backgroundImageUri = backgroundImageUri
            )
        }
    }
}

@Composable
fun BackgroundLayer(
    backgroundImageUri: Uri?,
    imagePickerLauncher: androidx.activity.result.ActivityResultLauncher<String>,
    onShowDialog: () -> Unit,
    onToggleMagnifier: () -> Unit,
    showMagnifier: Boolean,
    dialogOffset: Offset,
    showDialog: Boolean
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // 背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "背景图片",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        }

        // 控制按钮
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 左下角按钮
            Button(
                onClick = { imagePickerLauncher.launch("image/*") },
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(24.dp)
            ) {
                Text("选择图片")
            }

            // 中间放大镜开关按钮
            Button(
                onClick = onToggleMagnifier,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(24.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (showMagnifier) Color(0xFF4CAF50) else Color(0xFF2196F3)
                )
            ) {
                Text(if (showMagnifier) "关闭放大镜" else "打开放大镜")
            }

            // 右下角按钮
            Button(
                onClick = onShowDialog,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(24.dp)
            ) {
                Text("透镜效果")
            }
        }
    }
}

@Composable
fun RealTimeBlurDialog(
    backgroundImageUri: Uri?,
    onDismiss: () -> Unit,
    onOffsetChange: (Offset) -> Unit
) {
    // 拖动偏移状态
    var offset by remember { mutableStateOf(Offset.Zero) }

    // 获取屏幕尺寸
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidthPx = with(density) { configuration.screenWidthDp.dp.toPx() }
    val screenHeightPx = with(density) { configuration.screenHeightDp.dp.toPx() }

    // 通知父组件偏移变化
    LaunchedEffect(offset) {
        onOffsetChange(offset)
    }

    // 使用Box覆盖整个屏幕
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0f))
            .pointerInput(Unit) {
                // 点击空白区域关闭弹窗
                detectTapGestures {
                    onDismiss()
                }
            }
    ) {
        // 透镜效果的圆形弹窗
        // 外部容器，处理拖拽
        Box(
            modifier = Modifier
                .size(160.dp)  // 圆形透镜大小
                .offset { IntOffset(offset.x.roundToInt(), offset.y.roundToInt()) }
                .align(Alignment.Center)
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        offset += dragAmount
                    }
                }
        ) {
            // 透镜折射效果层（底层）
            LensRefractionEffect(
                backgroundImageUri = backgroundImageUri,
                lensOffset = offset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                lensRadius = 140.dp
            )

            // 160dp圆形 - 放大率8.8f
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect210(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (160 / 2).dp
                )
            }

            // 159dp圆形 - 放大率8.7f
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect209(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (159 / 2).dp
                )
            }

            // 158dp圆形 - 放大率8.6f
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect208(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (158 / 2).dp
                )
            }

            // 157dp圆形 - 放大率8.5f
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect207(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (157 / 2).dp
                )
            }

            // 156dp圆形 - 放大率8.4f
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect206(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (156 / 2).dp
                )
            }

            // 155dp圆形 - 放大率8.3f
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect205(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (155 / 2).dp
                )
            }

            // 154dp圆形 - 放大率8.2f
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect204(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (154 / 2).dp
                )
            }

            // 153dp圆形 - 放大率8.1f
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect203(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (153 / 2).dp
                )
            }

            // 152dp圆形 - 放大率8.0f
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect202(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (152 / 2).dp
                )
            }

            // 151dp圆形 - 放大率7.9f
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect201(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (151 / 2).dp
                )
            }

            // 149dp圆形 - 透明度7.8
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect199(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (149 / 2).dp
                )
            }

            // 148dp圆形 - 透明度7.6
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect198(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (148 / 2).dp
                )
            }

            // 147dp圆形 - 透明度7.4
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect197(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (147 / 2).dp
                )
            }

            // 146dp圆形 - 透明度7.2
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect196(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (146 / 2).dp
                )
            }

            // 145dp圆形 - 透明度7.0
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect195(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (145 / 2).dp
                )
            }

            // 144dp圆形 - 透明度6.8
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect194(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (144 / 2).dp
                )
            }

            // 143dp圆形 - 透明度6.6
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect193(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (143 / 2).dp
                )
            }

            // 142dp圆形 - 透明度6.4
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect192(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (142 / 2).dp
                )
            }

            // 141dp圆形 - 透明度6.2
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect191(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (141 / 2).dp
                )
            }

            // 140dp圆形 - 6f放大率（中下层，同心圆）
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                MidBottomLensEffect(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (140 / 2).dp
                )
            }

            // 189dp圆形 - 透明度6.0
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect189(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (139 / 2).dp
                )
            }

            // 188dp圆形 - 透明度5.8
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect188(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (138 / 2).dp
                )
            }

            // 187dp圆形 - 透明度5.6
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect187(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (137 / 2).dp
                )
            }

            // 186dp圆形 - 透明度5.4
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect186(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (136 / 2).dp
                )
            }

            // 185dp圆形 - 透明度5.2
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect185(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (135 / 2).dp
                )
            }

            // 184dp圆形 - 透明度5.0
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect184(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (134 / 2).dp
                )
            }

            // 183dp圆形 - 透明度4.8
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect183(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (133 / 2).dp
                )
            }

            // 182dp圆形 - 透明度4.6
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect182(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (132 / 2).dp
                )
            }

            // 181dp圆形 - 透明度4.4
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect181(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (131 / 2).dp
                )
            }

            // 180dp圆形 - 透明度4.2
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect180(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (130 / 2).dp
                )
            }

            // 179dp圆形 - 透明度4.0
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect179(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (129 / 2).dp
                )
            }

            // 178dp圆形 - 透明度3.8
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect178(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (128 / 2).dp
                )
            }

            // 177dp圆形 - 透明度3.6
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect177(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (127 / 2).dp
                )
            }

            // 176dp圆形 - 透明度3.4
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect176(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (126 / 2).dp
                )
            }

            // 175dp圆形 - 4f放大率（中上层，同心圆）
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                MidTopLensEffect2(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (125 / 2).dp
                )
            }

            // 174dp圆形 - 透明度3.2
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect174(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (124 / 2).dp
                )
            }

            // 173dp圆形 - 透明度3.0
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect173(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (123 / 2).dp
                )
            }

            // 172dp圆形 - 透明度2.8
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect172(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (122 / 2).dp
                )
            }

            // 171dp圆形 - 透明度2.6
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect171(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (121 / 2).dp
                )
            }

            // 170dp圆形 - 2f放大率（中上层，同心圆）
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                MidTopLensEffect(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (120 / 2).dp // 170dp直径 = 85dp半径
                )
            }

            // 169dp圆形 - 透明度2.4
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect169(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (119 / 2).dp
                )
            }

            // 168dp圆形 - 透明度2.2
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect168(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (118 / 2).dp
                )
            }

            // 167dp圆形 - 透明度2.0
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect167(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (117 / 2).dp
                )
            }

            // 166dp圆形 - 透明度1.8
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NewLensEffect166(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (116 / 2).dp
                )
            }

            // 165dp圆形 - 2f放大率（上层，同心圆）
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                TopLensEffect2(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (115 / 2).dp
                )
            }

            // 164dp圆形 - 透明度1.6


            // 最上层160dp圆形 - 1f放大率（最顶层，同心圆）
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                TopLensEffect(
                    backgroundImageUri = backgroundImageUri,
                    lensOffset = offset,
                    screenWidthPx = screenWidthPx,
                    screenHeightPx = screenHeightPx,
                    lensRadius = (0 / 2).dp // 160dp直径 = 80dp半径
                )
            }

            // 透镜边缘折射效果
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape)
                    .border(
                        width = 8.dp,
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color.White.copy(alpha = 0.8f),
                                Color.White.copy(alpha = 0.4f),
                                Color.Cyan.copy(alpha = 0.3f),
                                Color.Blue.copy(alpha = 0.2f),
                                Color.Transparent
                            ),
                            radius = 140.dp.value
                        ),
                        shape = CircleShape
                    )
            )

            // 透镜表面反光效果
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color.White.copy(alpha = 0.3f),
                                Color.Transparent,
                                Color.White.copy(alpha = 0.1f)
                            ),
                            center = Offset(0.3f, 0.3f),
                            radius = 200f
                        )
                    )
            )

            // 中心文本
            Text(
                text = "希望鱼",
                color = Color.White,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.align(Alignment.Center),
                textAlign = TextAlign.Center,
                style = TextStyle(
                    shadow = Shadow(
                        color = Color.Black.copy(alpha = 0.5f),
                        offset = Offset(2f, 2f),
                        blurRadius = 4f
                    )
                )
            )
        }
    }
}

@Composable
fun LensRefractionEffect(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .clip(CircleShape)
    ) {
        // 使用变形的背景图片来模拟折射效果
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "透镜折射背景",
                modifier = Modifier
                    .graphicsLayer {
                        // 透镜放大效果
                        scaleX = 8f
                        scaleY = 8f

                        // 轻微的旋转来模拟光学扭曲
                        rotationZ = sin(lensOffset.x * 0.01f) * 0f

                        // 透镜的球面扭曲效果
                        cameraDistance = 8f
                        rotationX = sin(lensOffset.y * 0.005f) * 1.5f
                        rotationY = cos(lensOffset.x * 0.005f) * 1.5f
                    }
                    .blur(15.dp)
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            // 透镜的球面扭曲计算
                            val centerX = constraints.maxWidth / 2f
                            val centerY = constraints.maxHeight / 2f
                            val radius = lensRadiusPx

                            // 计算透镜中心相对于屏幕的位置
                            val baseLensOffsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2
                            val baseLensOffsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2

                            // 添加球面透镜的非线性扭曲
                            val distanceFromCenter = sqrt(lensOffset.x * lensOffset.x + lensOffset.y * lensOffset.y)
                            val distortionFactor = (1f - (distanceFromCenter / radius).coerceIn(0f, 1f)) * 0.3f

                            val sphericalDistortionX = (lensOffset.x * distortionFactor * 0.1f).roundToInt()
                            val sphericalDistortionY = (lensOffset.y * distortionFactor * 0.1f).roundToInt()

                            placeable.place(
                                baseLensOffsetX + sphericalDistortionX,
                                baseLensOffsetY + sphericalDistortionY
                            )
                        }
                    },
                contentScale = ContentScale.Crop
            )
        }

        // 添加透镜的色散和边缘效果
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            val centerX = size.width / 2
            val centerY = size.height / 2

            // 绘制色散光谱效果 - 模拟光的色散
            for (angle in 0 until 360 step 5) {
                val radians = angle * PI / 180
                val innerRadius = lensRadiusPx * 0.85f
                val outerRadius = lensRadiusPx * 0.98f

                // 色散效果 - 不同颜色在不同半径
                val redRadius = innerRadius + (outerRadius - innerRadius) * 0.7f
                val greenRadius = innerRadius + (outerRadius - innerRadius) * 0.85f
                val blueRadius = innerRadius + (outerRadius - innerRadius) * 1.0f

                val baseX = centerX + cos(radians).toFloat()
                val baseY = centerY + sin(radians).toFloat()

                // 红色色散
                drawCircle(
                    color = Color.Red.copy(alpha = 0.15f),
                    radius = 1.5f,
                    center = Offset(baseX * redRadius / innerRadius, baseY * redRadius / innerRadius)
                )

                // 绿色色散
                drawCircle(
                    color = Color.Green.copy(alpha = 0.12f),
                    radius = 1.2f,
                    center = Offset(baseX * greenRadius / innerRadius, baseY * greenRadius / innerRadius)
                )

                // 蓝色色散
                drawCircle(
                    color = Color.Blue.copy(alpha = 0.1f),
                    radius = 1.0f,
                    center = Offset(baseX * blueRadius / innerRadius, baseY * blueRadius / innerRadius)
                )
            }

            // 透镜边缘的焦散线效果
            for (i in 0 until 12) {
                val angle = i * 30f * PI / 180
                val startRadius = lensRadiusPx * 0.6f
                val endRadius = lensRadiusPx * 0.9f

                val startX = centerX + cos(angle).toFloat() * startRadius
                val startY = centerY + sin(angle).toFloat() * startRadius
                val endX = centerX + cos(angle).toFloat() * endRadius
                val endY = centerY + sin(angle).toFloat() * endRadius

                drawLine(
                    color = Color.White.copy(alpha = 0.2f),
                    start = Offset(startX, startY),
                    end = Offset(endX, endY),
                    strokeWidth = 0.8f
                )
            }
        }
    }
}

@Composable
fun InnerLensEffect(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    // 180dp内部圆形透镜（同心圆）
    Box(
        modifier = Modifier
            .size(lensRadius * 2) // 180dp直径
            .clip(CircleShape)
    ) {
        // 内部透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "内部透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 1.2f放大效果
                        scaleX = 4f
                        scaleY = 4f

                        // 轻微的透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.005f) * 1f
                        cameraDistance = 12f
                        rotationX = sin(lensOffset.y * 0.003f) * 2f
                        rotationY = cos(lensOffset.x * 0.003f) * 2f

                        // 透明度
                        alpha = 0.9f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            // 内部透镜的位置计算
                            val innerOffsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2
                            val innerOffsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2

                            placeable.place(innerOffsetX, innerOffsetY)
                        }
                    }
                    .blur(50.dp), // 非常轻微的模糊
                contentScale = ContentScale.Crop
            )
        }

        // 内部透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 2.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.5f),
                            Color.White.copy(alpha = 0.2f),
                            Color.Cyan.copy(alpha = 0.15f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 内部透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.2f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.05f)
                        ),
                        center = Offset(0.4f, 0.4f),
                        radius = lensRadiusPx * 0.8f
                    )
                )
        )
    }
}

@Composable
fun TopLensEffect2(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    // 165dp上层圆形透镜
    Box(
        modifier = Modifier
            .size(lensRadius * 2) // 165dp直径
            .clip(CircleShape)
    ) {
        // 上层透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "165dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 2f放大效果
                        scaleX = 2f
                        scaleY = 2f

                        // 轻微的透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.003f) * 1.5f
                        cameraDistance = 12f
                        rotationX = sin(lensOffset.y * 0.002f) * 2f
                        rotationY = cos(lensOffset.x * 0.002f) * 2f

                        // 透明度
                        alpha = 0.85f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            // 上层透镜的位置计算，加入轻微扭曲
                            val distortionX = sin(lensOffset.y * 0.008f) * 3f
                            val distortionY = cos(lensOffset.x * 0.008f) * 3f

                            val topOffsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val topOffsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(topOffsetX, topOffsetY)
                        }
                    }
                    .blur(10.dp), // 轻微的模糊
                contentScale = ContentScale.Crop
            )
        }

        // 上层透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 1.8.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.45f),
                            Color.White.copy(alpha = 0.25f),
                            Color.Cyan.copy(alpha = 0.15f),
                            Color.Blue.copy(alpha = 0.08f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 上层透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.15f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.04f)
                        ),
                        center = Offset(0.35f, 0.35f),
                        radius = lensRadiusPx * 0.7f
                    )
                )
        )
    }
}

@Composable
fun TopLensEffect(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    // 160dp最上层圆形透镜
    Box(
        modifier = Modifier
            .size(lensRadius * 2) // 160dp直径
            .clip(CircleShape)
    ) {
        // 最上层透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "最上层透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 1f放大效果（不放大）
                        scaleX = 3f
                        scaleY = 3f

                        // 非常轻微的透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.002f) * 0.5f
                        cameraDistance = 15f
                        rotationX = sin(lensOffset.y * 0.001f) * 1f
                        rotationY = cos(lensOffset.x * 0.001f) * 1f

                        // 透明度
                        alpha = 0.95f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            // 最上层透镜的位置计算
                            val topOffsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2
                            val topOffsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2

                            placeable.place(topOffsetX, topOffsetY)
                        }
                    }
                    .blur(50.dp), // 极轻微的模糊
                contentScale = ContentScale.Crop
            )
        }

        // 最上层透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 1.5.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.4f),
                            Color.White.copy(alpha = 0.15f),
                            Color.Cyan.copy(alpha = 0.1f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 最上层透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.15f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.03f)
                        ),
                        center = Offset(0.5f, 0.5f),
                        radius = lensRadiusPx * 0.6f
                    )
                )
        )
    }
}

@Composable
fun MidTopLensEffect2(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    // 175dp中上层圆形透镜
    Box(
        modifier = Modifier
            .size(lensRadius * 2) // 175dp直径
            .clip(CircleShape)
    ) {
        // 中上层透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "175dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 4f放大效果
                        scaleX = 4f
                        scaleY = 4f

                        // 中等的透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.005f) * 2.5f
                        cameraDistance = 8f
                        rotationX = sin(lensOffset.y * 0.003f) * 3.5f
                        rotationY = cos(lensOffset.x * 0.003f) * 3.5f

                        // 透明度
                        alpha = 0.88f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            // 中上层透镜的位置计算，加入扭曲
                            val distortionX = sin(lensOffset.y * 0.011f) * 5f
                            val distortionY = cos(lensOffset.x * 0.011f) * 5f

                            val midTopOffsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val midTopOffsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(midTopOffsetX, midTopOffsetY)
                        }
                    }
                    .blur(10.dp), // 轻微的模糊, // 适中的模糊
                contentScale = ContentScale.Crop
            )
        }

        // 中上层透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 2.2.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.5f),
                            Color.White.copy(alpha = 0.3f),
                            Color.Cyan.copy(alpha = 0.2f),
                            Color.Blue.copy(alpha = 0.1f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 中上层透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.2f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.06f)
                        ),
                        center = Offset(0.4f, 0.4f),
                        radius = lensRadiusPx * 0.75f
                    )
                )
        )
    }
}

@Composable
fun MidTopLensEffect(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    // 170dp中上层圆形透镜
    Box(
        modifier = Modifier
            .size(lensRadius * 2) // 170dp直径
            .clip(CircleShape)
    ) {
        // 中上层透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "170dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 2f放大效果
                        scaleX = 2f
                        scaleY = 2f

                        // 适中的透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.004f) * 1.5f
                        cameraDistance = 12f
                        rotationX = sin(lensOffset.y * 0.002f) * 2.5f
                        rotationY = cos(lensOffset.x * 0.002f) * 2.5f

                        // 透明度
                        alpha = 0.88f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            // 中上层透镜的位置计算
                            val midTopOffsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2
                            val midTopOffsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2

                            placeable.place(midTopOffsetX, midTopOffsetY)
                        }
                    }
                    .blur(10.dp), // 轻微的模糊), // 轻微模糊
                contentScale = ContentScale.Crop
            )
        }

        // 中上层透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 1.8.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.45f),
                            Color.White.copy(alpha = 0.25f),
                            Color.Cyan.copy(alpha = 0.12f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 中上层透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.18f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.04f)
                        ),
                        center = Offset(0.45f, 0.45f),
                        radius = lensRadiusPx * 0.7f
                    )
                )
        )
    }
}

@Composable
fun MidBottomLensEffect(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    // 190dp中下层圆形透镜
    Box(
        modifier = Modifier
            .size(lensRadius * 2) // 190dp直径
            .clip(CircleShape)
    ) {
        // 中下层透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "190dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 6f放大效果
                        scaleX = 6f
                        scaleY = 6f

                        // 较强的透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.006f) * 3f
                        cameraDistance = 8f
                        rotationX = sin(lensOffset.y * 0.004f) * 4f
                        rotationY = cos(lensOffset.x * 0.004f) * 4f

                        // 透明度
                        alpha = 0.92f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            // 中下层透镜的位置计算，加入更多扭曲
                            val distortionX = sin(lensOffset.y * 0.012f) * 6f
                            val distortionY = cos(lensOffset.x * 0.012f) * 6f

                            val midBottomOffsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val midBottomOffsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(midBottomOffsetX, midBottomOffsetY)
                        }
                    }
                    .blur(12.dp), // 较明显的模糊
                contentScale = ContentScale.Crop
            )
        }

        // 中下层透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 2.5.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.55f),
                            Color.White.copy(alpha = 0.35f),
                            Color.Cyan.copy(alpha = 0.25f),
                            Color.Blue.copy(alpha = 0.15f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 中下层透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.25f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.08f)
                        ),
                        center = Offset(0.35f, 0.35f),
                        radius = lensRadiusPx * 0.75f
                    )
                )
        )
    }
}

@Composable
fun NewLensEffect199(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        // 199dp透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "199dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 7.8f放大效果
                        scaleX = 7.8f
                        scaleY = 7.8f

                        // 透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.005f) * 2f
                        cameraDistance = 10f
                        rotationX = sin(lensOffset.y * 0.003f) * 3f
                        rotationY = cos(lensOffset.x * 0.003f) * 3f

                        // 透明度7.8
                        alpha = 0.78f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.010f) * 4f
                            val distortionY = cos(lensOffset.x * 0.010f) * 4f

                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(10.dp), // 轻微的模糊,
                contentScale = ContentScale.Crop
            )
        }

        // 199dp透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 1.8.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.6f),
                            Color.White.copy(alpha = 0.4f),
                            Color.Cyan.copy(alpha = 0.3f),
                            Color.Blue.copy(alpha = 0.2f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 199dp透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.28f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.09f)
                        ),
                        center = Offset(0.32f, 0.32f),
                        radius = lensRadiusPx * 0.8f
                    )
                )
        )
    }
}

@Composable
fun NewLensEffect198(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        // 198dp透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "198dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 7.6f放大效果
                        scaleX = 7.6f
                        scaleY = 7.6f

                        // 透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.0048f) * 2.2f
                        cameraDistance = 10.2f
                        rotationX = sin(lensOffset.y * 0.0032f) * 3.2f
                        rotationY = cos(lensOffset.x * 0.0032f) * 3.2f

                        // 透明度7.6
                        alpha = 0.76f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0098f) * 4.2f
                            val distortionY = cos(lensOffset.x * 0.0098f) * 4.2f

                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(0.9.dp),
                contentScale = ContentScale.Crop
            )
        }

        // 198dp透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 1.9.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.58f),
                            Color.White.copy(alpha = 0.38f),
                            Color.Cyan.copy(alpha = 0.28f),
                            Color.Blue.copy(alpha = 0.18f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 198dp透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.26f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.08f)
                        ),
                        center = Offset(0.33f, 0.33f),
                        radius = lensRadiusPx * 0.78f
                    )
                )
        )
    }
}

@Composable
fun NewLensEffect197(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        // 197dp透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "197dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 7.4f放大效果
                        scaleX = 7.4f
                        scaleY = 7.4f

                        // 透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.0046f) * 2.4f
                        cameraDistance = 10.4f
                        rotationX = sin(lensOffset.y * 0.0034f) * 3.4f
                        rotationY = cos(lensOffset.x * 0.0034f) * 3.4f

                        // 透明度7.4
                        alpha = 0.74f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0096f) * 4.4f
                            val distortionY = cos(lensOffset.x * 0.0096f) * 4.4f

                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.0.dp),
                contentScale = ContentScale.Crop
            )
        }

        // 197dp透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 2.0.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.56f),
                            Color.White.copy(alpha = 0.36f),
                            Color.Cyan.copy(alpha = 0.26f),
                            Color.Blue.copy(alpha = 0.16f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 197dp透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.24f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.07f)
                        ),
                        center = Offset(0.34f, 0.34f),
                        radius = lensRadiusPx * 0.76f
                    )
                )
        )
    }
}

@Composable
fun NewLensEffect196(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        // 196dp透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "196dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 7.2f放大效果
                        scaleX = 7.2f
                        scaleY = 7.2f

                        // 透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.0044f) * 2.6f
                        cameraDistance = 10.6f
                        rotationX = sin(lensOffset.y * 0.0036f) * 3.6f
                        rotationY = cos(lensOffset.x * 0.0036f) * 3.6f

                        // 透明度7.2
                        alpha = 0.72f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0094f) * 4.6f
                            val distortionY = cos(lensOffset.x * 0.0094f) * 4.6f

                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.1.dp),
                contentScale = ContentScale.Crop
            )
        }

        // 196dp透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 2.1.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.54f),
                            Color.White.copy(alpha = 0.34f),
                            Color.Cyan.copy(alpha = 0.24f),
                            Color.Blue.copy(alpha = 0.14f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 196dp透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.22f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.06f)
                        ),
                        center = Offset(0.35f, 0.35f),
                        radius = lensRadiusPx * 0.74f
                    )
                )
        )
    }
}

@Composable
fun NewLensEffect195(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        // 195dp透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "195dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 7.0f放大效果
                        scaleX = 7.0f
                        scaleY = 7.0f

                        // 透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.0042f) * 2.8f
                        cameraDistance = 10.8f
                        rotationX = sin(lensOffset.y * 0.0038f) * 3.8f
                        rotationY = cos(lensOffset.x * 0.0038f) * 3.8f

                        // 透明度7.0
                        alpha = 0.70f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0092f) * 4.8f
                            val distortionY = cos(lensOffset.x * 0.0092f) * 4.8f

                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.2.dp),
                contentScale = ContentScale.Crop
            )
        }

        // 195dp透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 2.2.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.52f),
                            Color.White.copy(alpha = 0.32f),
                            Color.Cyan.copy(alpha = 0.22f),
                            Color.Blue.copy(alpha = 0.12f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 195dp透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.20f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.05f)
                        ),
                        center = Offset(0.36f, 0.36f),
                        radius = lensRadiusPx * 0.72f
                    )
                )
        )
    }
}

@Composable
fun NewLensEffect194(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        // 194dp透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "194dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 6.8f放大效果
                        scaleX = 6.8f
                        scaleY = 6.8f

                        // 透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.0040f) * 3.0f
                        cameraDistance = 11.0f
                        rotationX = sin(lensOffset.y * 0.0040f) * 4.0f
                        rotationY = cos(lensOffset.x * 0.0040f) * 4.0f

                        // 透明度6.8
                        alpha = 0.68f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0090f) * 5.0f
                            val distortionY = cos(lensOffset.x * 0.0090f) * 5.0f

                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.3.dp),
                contentScale = ContentScale.Crop
            )
        }

        // 194dp透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 2.3.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.50f),
                            Color.White.copy(alpha = 0.30f),
                            Color.Cyan.copy(alpha = 0.20f),
                            Color.Blue.copy(alpha = 0.10f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 194dp透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.18f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.04f)
                        ),
                        center = Offset(0.37f, 0.37f),
                        radius = lensRadiusPx * 0.70f
                    )
                )
        )
    }
}

@Composable
fun NewLensEffect193(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        // 193dp透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "193dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 6.6f放大效果
                        scaleX = 6.6f
                        scaleY = 6.6f

                        // 透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.0038f) * 3.2f
                        cameraDistance = 11.2f
                        rotationX = sin(lensOffset.y * 0.0042f) * 4.2f
                        rotationY = cos(lensOffset.x * 0.0042f) * 4.2f

                        // 透明度6.6
                        alpha = 0.66f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0088f) * 5.2f
                            val distortionY = cos(lensOffset.x * 0.0088f) * 5.2f

                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.4.dp),
                contentScale = ContentScale.Crop
            )
        }

        // 193dp透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 2.4.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.48f),
                            Color.White.copy(alpha = 0.28f),
                            Color.Cyan.copy(alpha = 0.18f),
                            Color.Blue.copy(alpha = 0.08f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 193dp透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.16f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.03f)
                        ),
                        center = Offset(0.38f, 0.38f),
                        radius = lensRadiusPx * 0.68f
                    )
                )
        )
    }
}

@Composable
fun NewLensEffect192(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        // 192dp透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "192dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 6.4f放大效果
                        scaleX = 6.4f
                        scaleY = 6.4f

                        // 透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.0036f) * 3.4f
                        cameraDistance = 11.4f
                        rotationX = sin(lensOffset.y * 0.0044f) * 4.4f
                        rotationY = cos(lensOffset.x * 0.0044f) * 4.4f

                        // 透明度6.4
                        alpha = 0.64f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0086f) * 5.4f
                            val distortionY = cos(lensOffset.x * 0.0086f) * 5.4f

                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.5.dp),
                contentScale = ContentScale.Crop
            )
        }

        // 192dp透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 2.5.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.46f),
                            Color.White.copy(alpha = 0.26f),
                            Color.Cyan.copy(alpha = 0.16f),
                            Color.Blue.copy(alpha = 0.06f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 192dp透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.14f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.02f)
                        ),
                        center = Offset(0.39f, 0.39f),
                        radius = lensRadiusPx * 0.66f
                    )
                )
        )
    }
}

@Composable
fun NewLensEffect191(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        // 191dp透镜的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "191dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 6.2f放大效果
                        scaleX = 6.2f
                        scaleY = 6.2f

                        // 透镜扭曲
                        rotationZ = sin(lensOffset.x * 0.0034f) * 3.6f
                        cameraDistance = 11.6f
                        rotationX = sin(lensOffset.y * 0.0046f) * 4.6f
                        rotationY = cos(lensOffset.x * 0.0046f) * 4.6f

                        // 透明度6.2
                        alpha = 0.62f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0084f) * 5.6f
                            val distortionY = cos(lensOffset.x * 0.0084f) * 5.6f

                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()

                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.6.dp),
                contentScale = ContentScale.Crop
            )
        }

        // 191dp透镜的边缘效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 2.6.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.44f),
                            Color.White.copy(alpha = 0.24f),
                            Color.Cyan.copy(alpha = 0.14f),
                            Color.Blue.copy(alpha = 0.04f),
                            Color.Transparent
                        ),
                        radius = lensRadiusPx
                    ),
                    shape = CircleShape
                )
        )

        // 191dp透镜的反光效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.12f),
                            Color.Transparent,
                            Color.White.copy(alpha = 0.01f)
                        ),
                        center = Offset(0.40f, 0.40f),
                        radius = lensRadiusPx * 0.64f
                    )
                )
        )
    }
}

@Composable
fun NewLensEffect189(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "189dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 6.0f
                        scaleY = 6.0f
                        rotationZ = sin(lensOffset.x * 0.0032f) * 3.8f
                        cameraDistance = 11.8f
                        rotationX = sin(lensOffset.y * 0.0048f) * 4.8f
                        rotationY = cos(lensOffset.x * 0.0048f) * 4.8f
                        alpha = 0.60f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0082f) * 5.8f
                            val distortionY = cos(lensOffset.x * 0.0082f) * 5.8f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.7.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(2.7.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.42f), Color.White.copy(alpha = 0.22f),
                        Color.Cyan.copy(alpha = 0.12f), Color.Blue.copy(alpha = 0.02f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.10f), Color.Transparent, Color.White.copy(alpha = 0.005f)),
                    center = Offset(0.41f, 0.41f), radius = lensRadiusPx * 0.62f))
        )
    }
}

@Composable
fun NewLensEffect188(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "188dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 5.8f
                        scaleY = 5.8f
                        rotationZ = sin(lensOffset.x * 0.0030f) * 4.0f
                        cameraDistance = 12.0f
                        rotationX = sin(lensOffset.y * 0.0050f) * 5.0f
                        rotationY = cos(lensOffset.x * 0.0050f) * 5.0f
                        alpha = 0.58f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0080f) * 6.0f
                            val distortionY = cos(lensOffset.x * 0.0080f) * 6.0f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.8.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(2.8.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.40f), Color.White.copy(alpha = 0.20f),
                        Color.Cyan.copy(alpha = 0.10f), Color.Blue.copy(alpha = 0.01f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.08f), Color.Transparent, Color.White.copy(alpha = 0.004f)),
                    center = Offset(0.42f, 0.42f), radius = lensRadiusPx * 0.60f))
        )
    }
}

@Composable
fun NewLensEffect187(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "187dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 5.6f
                        scaleY = 5.6f
                        rotationZ = sin(lensOffset.x * 0.0028f) * 4.2f
                        cameraDistance = 12.2f
                        rotationX = sin(lensOffset.y * 0.0052f) * 5.2f
                        rotationY = cos(lensOffset.x * 0.0052f) * 5.2f
                        alpha = 0.56f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0078f) * 6.2f
                            val distortionY = cos(lensOffset.x * 0.0078f) * 6.2f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.9.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(2.9.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.38f), Color.White.copy(alpha = 0.18f),
                        Color.Cyan.copy(alpha = 0.08f), Color.Blue.copy(alpha = 0.005f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.06f), Color.Transparent, Color.White.copy(alpha = 0.003f)),
                    center = Offset(0.43f, 0.43f), radius = lensRadiusPx * 0.58f))
        )
    }
}

@Composable
fun NewLensEffect186(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "186dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 5.4f
                        scaleY = 5.4f
                        rotationZ = sin(lensOffset.x * 0.0026f) * 4.4f
                        cameraDistance = 12.4f
                        rotationX = sin(lensOffset.y * 0.0054f) * 5.4f
                        rotationY = cos(lensOffset.x * 0.0054f) * 5.4f
                        alpha = 0.54f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0076f) * 6.4f
                            val distortionY = cos(lensOffset.x * 0.0076f) * 6.4f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(2.0.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(3.0.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.36f), Color.White.copy(alpha = 0.16f),
                        Color.Cyan.copy(alpha = 0.06f), Color.Blue.copy(alpha = 0.004f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.04f), Color.Transparent, Color.White.copy(alpha = 0.002f)),
                    center = Offset(0.44f, 0.44f), radius = lensRadiusPx * 0.56f))
        )
    }
}

@Composable
fun NewLensEffect185(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "185dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 5.2f
                        scaleY = 5.2f
                        rotationZ = sin(lensOffset.x * 0.0024f) * 4.6f
                        cameraDistance = 12.6f
                        rotationX = sin(lensOffset.y * 0.0056f) * 5.6f
                        rotationY = cos(lensOffset.x * 0.0056f) * 5.6f
                        alpha = 0.52f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0074f) * 6.6f
                            val distortionY = cos(lensOffset.x * 0.0074f) * 6.6f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(2.1.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(3.1.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.34f), Color.White.copy(alpha = 0.14f),
                        Color.Cyan.copy(alpha = 0.04f), Color.Blue.copy(alpha = 0.003f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.02f), Color.Transparent, Color.White.copy(alpha = 0.001f)),
                    center = Offset(0.45f, 0.45f), radius = lensRadiusPx * 0.54f))
        )
    }
}

@Composable
fun NewLensEffect184(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "184dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 5.0f
                        scaleY = 5.0f
                        rotationZ = sin(lensOffset.x * 0.0022f) * 4.8f
                        cameraDistance = 12.8f
                        rotationX = sin(lensOffset.y * 0.0058f) * 5.8f
                        rotationY = cos(lensOffset.x * 0.0058f) * 5.8f
                        alpha = 0.50f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0072f) * 6.8f
                            val distortionY = cos(lensOffset.x * 0.0072f) * 6.8f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(2.2.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(3.2.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.32f), Color.White.copy(alpha = 0.12f),
                        Color.Cyan.copy(alpha = 0.02f), Color.Blue.copy(alpha = 0.002f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.015f), Color.Transparent, Color.White.copy(alpha = 0.0005f)),
                    center = Offset(0.46f, 0.46f), radius = lensRadiusPx * 0.52f))
        )
    }
}

@Composable
fun NewLensEffect183(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "183dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 4.8f
                        scaleY = 4.8f
                        rotationZ = sin(lensOffset.x * 0.0020f) * 5.0f
                        cameraDistance = 13.0f
                        rotationX = sin(lensOffset.y * 0.0060f) * 6.0f
                        rotationY = cos(lensOffset.x * 0.0060f) * 6.0f
                        alpha = 0.48f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0070f) * 7.0f
                            val distortionY = cos(lensOffset.x * 0.0070f) * 7.0f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(2.3.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(3.3.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.30f), Color.White.copy(alpha = 0.10f),
                        Color.Cyan.copy(alpha = 0.015f), Color.Blue.copy(alpha = 0.001f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.01f), Color.Transparent, Color.White.copy(alpha = 0.0003f)),
                    center = Offset(0.47f, 0.47f), radius = lensRadiusPx * 0.50f))
        )
    }
}

@Composable
fun NewLensEffect182(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "182dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 4.6f
                        scaleY = 4.6f
                        rotationZ = sin(lensOffset.x * 0.0018f) * 5.2f
                        cameraDistance = 13.2f
                        rotationX = sin(lensOffset.y * 0.0062f) * 6.2f
                        rotationY = cos(lensOffset.x * 0.0062f) * 6.2f
                        alpha = 0.46f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0068f) * 7.2f
                            val distortionY = cos(lensOffset.x * 0.0068f) * 7.2f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(2.4.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(3.4.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.28f), Color.White.copy(alpha = 0.08f),
                        Color.Cyan.copy(alpha = 0.01f), Color.Blue.copy(alpha = 0.0005f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.005f), Color.Transparent, Color.White.copy(alpha = 0.0001f)),
                    center = Offset(0.48f, 0.48f), radius = lensRadiusPx * 0.48f))
        )
    }
}

@Composable
fun NewLensEffect181(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "181dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 4.4f
                        scaleY = 4.4f
                        rotationZ = sin(lensOffset.x * 0.0016f) * 5.4f
                        cameraDistance = 13.4f
                        rotationX = sin(lensOffset.y * 0.0064f) * 6.4f
                        rotationY = cos(lensOffset.x * 0.0064f) * 6.4f
                        alpha = 0.44f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0066f) * 7.4f
                            val distortionY = cos(lensOffset.x * 0.0066f) * 7.4f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(2.5.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(3.5.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.26f), Color.White.copy(alpha = 0.06f),
                        Color.Cyan.copy(alpha = 0.005f), Color.Blue.copy(alpha = 0.0003f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.003f), Color.Transparent, Color.White.copy(alpha = 0.00005f)),
                    center = Offset(0.49f, 0.49f), radius = lensRadiusPx * 0.46f))
        )
    }
}

@Composable
fun NewLensEffect180(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "180dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 4.2f
                        scaleY = 4.2f
                        rotationZ = sin(lensOffset.x * 0.0014f) * 5.6f
                        cameraDistance = 13.6f
                        rotationX = sin(lensOffset.y * 0.0066f) * 6.6f
                        rotationY = cos(lensOffset.x * 0.0066f) * 6.6f
                        alpha = 0.42f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0064f) * 7.6f
                            val distortionY = cos(lensOffset.x * 0.0064f) * 7.6f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(2.6.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(3.6.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.24f), Color.White.copy(alpha = 0.04f),
                        Color.Cyan.copy(alpha = 0.003f), Color.Blue.copy(alpha = 0.0002f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.002f), Color.Transparent, Color.White.copy(alpha = 0.00003f)),
                    center = Offset(0.50f, 0.50f), radius = lensRadiusPx * 0.44f))
        )
    }
}

@Composable
fun NewLensEffect179(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "179dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 4.0f
                        scaleY = 4.0f
                        rotationZ = sin(lensOffset.x * 0.0012f) * 5.8f
                        cameraDistance = 13.8f
                        rotationX = sin(lensOffset.y * 0.0068f) * 6.8f
                        rotationY = cos(lensOffset.x * 0.0068f) * 6.8f
                        alpha = 0.40f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0062f) * 7.8f
                            val distortionY = cos(lensOffset.x * 0.0062f) * 7.8f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(2.7.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(3.7.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.22f), Color.White.copy(alpha = 0.03f),
                        Color.Cyan.copy(alpha = 0.002f), Color.Blue.copy(alpha = 0.0001f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.001f), Color.Transparent, Color.White.copy(alpha = 0.00002f)),
                    center = Offset(0.51f, 0.51f), radius = lensRadiusPx * 0.42f))
        )
    }
}

@Composable
fun NewLensEffect178(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "178dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 3.8f
                        scaleY = 3.8f
                        rotationZ = sin(lensOffset.x * 0.0010f) * 6.0f
                        cameraDistance = 14.0f
                        rotationX = sin(lensOffset.y * 0.0070f) * 7.0f
                        rotationY = cos(lensOffset.x * 0.0070f) * 7.0f
                        alpha = 0.38f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0060f) * 8.0f
                            val distortionY = cos(lensOffset.x * 0.0060f) * 8.0f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(2.8.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(3.8.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.20f), Color.White.copy(alpha = 0.025f),
                        Color.Cyan.copy(alpha = 0.0015f), Color.Blue.copy(alpha = 0.00008f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.0008f), Color.Transparent, Color.White.copy(alpha = 0.00001f)),
                    center = Offset(0.52f, 0.52f), radius = lensRadiusPx * 0.40f))
        )
    }
}

@Composable
fun NewLensEffect177(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "177dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 3.6f
                        scaleY = 3.6f
                        rotationZ = sin(lensOffset.x * 0.0008f) * 6.2f
                        cameraDistance = 14.2f
                        rotationX = sin(lensOffset.y * 0.0072f) * 7.2f
                        rotationY = cos(lensOffset.x * 0.0072f) * 7.2f
                        alpha = 0.36f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0058f) * 8.2f
                            val distortionY = cos(lensOffset.x * 0.0058f) * 8.2f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(2.9.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(3.9.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.18f), Color.White.copy(alpha = 0.02f),
                        Color.Cyan.copy(alpha = 0.001f), Color.Blue.copy(alpha = 0.00006f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.0006f), Color.Transparent, Color.White.copy(alpha = 0.000008f)),
                    center = Offset(0.53f, 0.53f), radius = lensRadiusPx * 0.38f))
        )
    }
}

@Composable
fun NewLensEffect176(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "176dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 3.4f
                        scaleY = 3.4f
                        rotationZ = sin(lensOffset.x * 0.0006f) * 6.4f
                        cameraDistance = 14.4f
                        rotationX = sin(lensOffset.y * 0.0074f) * 7.4f
                        rotationY = cos(lensOffset.x * 0.0074f) * 7.4f
                        alpha = 0.34f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0056f) * 8.4f
                            val distortionY = cos(lensOffset.x * 0.0056f) * 8.4f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(3.0.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(4.0.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.16f), Color.White.copy(alpha = 0.015f),
                        Color.Cyan.copy(alpha = 0.0008f), Color.Blue.copy(alpha = 0.00004f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.0004f), Color.Transparent, Color.White.copy(alpha = 0.000006f)),
                    center = Offset(0.54f, 0.54f), radius = lensRadiusPx * 0.36f))
        )
    }
}

@Composable
fun NewLensEffect174(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "174dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 3.2f
                        scaleY = 3.2f
                        rotationZ = sin(lensOffset.x * 0.0004f) * 6.6f
                        cameraDistance = 14.6f
                        rotationX = sin(lensOffset.y * 0.0076f) * 7.6f
                        rotationY = cos(lensOffset.x * 0.0076f) * 7.6f
                        alpha = 0.32f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0054f) * 8.6f
                            val distortionY = cos(lensOffset.x * 0.0054f) * 8.6f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(3.1.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(4.1.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.14f), Color.White.copy(alpha = 0.01f),
                        Color.Cyan.copy(alpha = 0.0006f), Color.Blue.copy(alpha = 0.00002f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.0002f), Color.Transparent, Color.White.copy(alpha = 0.000004f)),
                    center = Offset(0.55f, 0.55f), radius = lensRadiusPx * 0.34f))
        )
    }
}

@Composable
fun NewLensEffect173(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "173dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 3.0f
                        scaleY = 3.0f
                        rotationZ = sin(lensOffset.x * 0.0002f) * 6.8f
                        cameraDistance = 14.8f
                        rotationX = sin(lensOffset.y * 0.0078f) * 7.8f
                        rotationY = cos(lensOffset.x * 0.0078f) * 7.8f
                        alpha = 0.30f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0052f) * 8.8f
                            val distortionY = cos(lensOffset.x * 0.0052f) * 8.8f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(3.2.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(4.2.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.12f), Color.White.copy(alpha = 0.008f),
                        Color.Cyan.copy(alpha = 0.0004f), Color.Blue.copy(alpha = 0.00001f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.0001f), Color.Transparent, Color.White.copy(alpha = 0.000002f)),
                    center = Offset(0.56f, 0.56f), radius = lensRadiusPx * 0.32f))
        )
    }
}

@Composable
fun NewLensEffect172(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "172dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 2.8f
                        scaleY = 2.8f
                        rotationZ = sin(lensOffset.x * 0.0001f) * 7.0f
                        cameraDistance = 15.0f
                        rotationX = sin(lensOffset.y * 0.0080f) * 8.0f
                        rotationY = cos(lensOffset.x * 0.0080f) * 8.0f
                        alpha = 0.28f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0050f) * 9.0f
                            val distortionY = cos(lensOffset.x * 0.0050f) * 9.0f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(3.3.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(4.3.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.10f), Color.White.copy(alpha = 0.006f),
                        Color.Cyan.copy(alpha = 0.0002f), Color.Blue.copy(alpha = 0.000008f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.00008f), Color.Transparent, Color.White.copy(alpha = 0.000001f)),
                    center = Offset(0.57f, 0.57f), radius = lensRadiusPx * 0.30f))
        )
    }
}

@Composable
fun NewLensEffect171(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "171dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 2.6f
                        scaleY = 2.6f
                        rotationZ = sin(lensOffset.x * 0.00005f) * 7.2f
                        cameraDistance = 15.2f
                        rotationX = sin(lensOffset.y * 0.0082f) * 8.2f
                        rotationY = cos(lensOffset.x * 0.0082f) * 8.2f
                        alpha = 0.26f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0048f) * 9.2f
                            val distortionY = cos(lensOffset.x * 0.0048f) * 9.2f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(3.4.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(4.4.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.08f), Color.White.copy(alpha = 0.004f),
                        Color.Cyan.copy(alpha = 0.0001f), Color.Blue.copy(alpha = 0.000006f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.00006f), Color.Transparent, Color.White.copy(alpha = 0.0000008f)),
                    center = Offset(0.58f, 0.58f), radius = lensRadiusPx * 0.28f))
        )
    }
}

@Composable
fun NewLensEffect169(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "169dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 2.4f
                        scaleY = 2.4f
                        rotationZ = sin(lensOffset.x * 0.00003f) * 7.4f
                        cameraDistance = 15.4f
                        rotationX = sin(lensOffset.y * 0.0084f) * 8.4f
                        rotationY = cos(lensOffset.x * 0.0084f) * 8.4f
                        alpha = 0.24f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0046f) * 9.4f
                            val distortionY = cos(lensOffset.x * 0.0046f) * 9.4f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(3.5.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(4.5.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.06f), Color.White.copy(alpha = 0.002f),
                        Color.Cyan.copy(alpha = 0.00008f), Color.Blue.copy(alpha = 0.000004f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.00004f), Color.Transparent, Color.White.copy(alpha = 0.0000006f)),
                    center = Offset(0.59f, 0.59f), radius = lensRadiusPx * 0.26f))
        )
    }
}

@Composable
fun NewLensEffect168(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "168dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 2.2f
                        scaleY = 2.2f
                        rotationZ = sin(lensOffset.x * 0.00001f) * 7.6f
                        cameraDistance = 15.6f
                        rotationX = sin(lensOffset.y * 0.0086f) * 8.6f
                        rotationY = cos(lensOffset.x * 0.0086f) * 8.6f
                        alpha = 0.22f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0044f) * 9.6f
                            val distortionY = cos(lensOffset.x * 0.0044f) * 9.6f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(3.6.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(4.6.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.04f), Color.White.copy(alpha = 0.0015f),
                        Color.Cyan.copy(alpha = 0.00006f), Color.Blue.copy(alpha = 0.000002f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.00002f), Color.Transparent, Color.White.copy(alpha = 0.0000004f)),
                    center = Offset(0.60f, 0.60f), radius = lensRadiusPx * 0.24f))
        )
    }
}

@Composable
fun NewLensEffect167(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "167dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 2.0f
                        scaleY = 2.0f
                        rotationZ = sin(lensOffset.x * 0.000008f) * 7.8f
                        cameraDistance = 15.8f
                        rotationX = sin(lensOffset.y * 0.0088f) * 8.8f
                        rotationY = cos(lensOffset.x * 0.0088f) * 8.8f
                        alpha = 0.20f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0042f) * 9.8f
                            val distortionY = cos(lensOffset.x * 0.0042f) * 9.8f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(3.7.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(4.7.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.02f), Color.White.copy(alpha = 0.001f),
                        Color.Cyan.copy(alpha = 0.00004f), Color.Blue.copy(alpha = 0.000001f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.00001f), Color.Transparent, Color.White.copy(alpha = 0.0000002f)),
                    center = Offset(0.61f, 0.61f), radius = lensRadiusPx * 0.22f))
        )
    }
}

@Composable
fun NewLensEffect166(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "166dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 1.8f
                        scaleY = 1.8f
                        rotationZ = sin(lensOffset.x * 0.000006f) * 8.0f
                        cameraDistance = 16.0f
                        rotationX = sin(lensOffset.y * 0.0090f) * 9.0f
                        rotationY = cos(lensOffset.x * 0.0090f) * 9.0f
                        alpha = 0.18f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0040f) * 10.0f
                            val distortionY = cos(lensOffset.x * 0.0040f) * 10.0f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(3.8.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(4.8.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.015f), Color.White.copy(alpha = 0.0008f),
                        Color.Cyan.copy(alpha = 0.00002f), Color.Blue.copy(alpha = 0.0000008f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.000008f), Color.Transparent, Color.White.copy(alpha = 0.0000001f)),
                    center = Offset(0.62f, 0.62f), radius = lensRadiusPx * 0.20f))
        )
    }
}

@Composable
fun NewLensEffect164(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "164dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 1.6f
                        scaleY = 1.6f
                        rotationZ = sin(lensOffset.x * 0.000004f) * 8.2f
                        cameraDistance = 16.2f
                        rotationX = sin(lensOffset.y * 0.0092f) * 9.2f
                        rotationY = cos(lensOffset.x * 0.0092f) * 9.2f
                        alpha = 0.16f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0038f) * 10.2f
                            val distortionY = cos(lensOffset.x * 0.0038f) * 10.2f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(3.9.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(4.9.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.01f), Color.White.copy(alpha = 0.0006f),
                        Color.Cyan.copy(alpha = 0.00001f), Color.Blue.copy(alpha = 0.0000006f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.000006f), Color.Transparent, Color.White.copy(alpha = 0.00000008f)),
                    center = Offset(0.63f, 0.63f), radius = lensRadiusPx * 0.18f))
        )
    }
}

@Composable
fun NewLensEffect163(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "163dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 1.4f
                        scaleY = 1.4f
                        rotationZ = sin(lensOffset.x * 0.000002f) * 8.4f
                        cameraDistance = 16.4f
                        rotationX = sin(lensOffset.y * 0.0094f) * 9.4f
                        rotationY = cos(lensOffset.x * 0.0094f) * 9.4f
                        alpha = 0.14f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0036f) * 10.4f
                            val distortionY = cos(lensOffset.x * 0.0036f) * 10.4f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(4.0.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(5.0.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.008f), Color.White.copy(alpha = 0.0004f),
                        Color.Cyan.copy(alpha = 0.000008f), Color.Blue.copy(alpha = 0.0000004f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.000004f), Color.Transparent, Color.White.copy(alpha = 0.00000006f)),
                    center = Offset(0.64f, 0.64f), radius = lensRadiusPx * 0.16f))
        )
    }
}

@Composable
fun NewLensEffect162(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "162dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 1.2f
                        scaleY = 1.2f
                        rotationZ = sin(lensOffset.x * 0.000001f) * 8.6f
                        cameraDistance = 16.6f
                        rotationX = sin(lensOffset.y * 0.0096f) * 9.6f
                        rotationY = cos(lensOffset.x * 0.0096f) * 9.6f
                        alpha = 0.12f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0034f) * 10.6f
                            val distortionY = cos(lensOffset.x * 0.0034f) * 10.6f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(4.1.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(5.1.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.006f), Color.White.copy(alpha = 0.0002f),
                        Color.Cyan.copy(alpha = 0.000006f), Color.Blue.copy(alpha = 0.0000002f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.000002f), Color.Transparent, Color.White.copy(alpha = 0.00000004f)),
                    center = Offset(0.65f, 0.65f), radius = lensRadiusPx * 0.14f))
        )
    }
}

@Composable
fun NewLensEffect161(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "161dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 1.0f
                        scaleY = 1.0f
                        rotationZ = sin(lensOffset.x * 0.0000008f) * 8.8f
                        cameraDistance = 16.8f
                        rotationX = sin(lensOffset.y * 0.0098f) * 9.8f
                        rotationY = cos(lensOffset.x * 0.0098f) * 9.8f
                        alpha = 0.10f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0032f) * 10.8f
                            val distortionY = cos(lensOffset.x * 0.0032f) * 10.8f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(4.2.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(5.2.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.004f), Color.White.copy(alpha = 0.0001f),
                        Color.Cyan.copy(alpha = 0.000004f), Color.Blue.copy(alpha = 0.0000001f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.000001f), Color.Transparent, Color.White.copy(alpha = 0.00000002f)),
                    center = Offset(0.66f, 0.66f), radius = lensRadiusPx * 0.12f))
        )
    }
}

@Composable
fun NewLensEffect210(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "210dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 8.8f
                        scaleY = 8.8f
                        rotationZ = sin(lensOffset.x * 0.0052f) * 1.8f
                        cameraDistance = 9.8f
                        rotationX = sin(lensOffset.y * 0.0028f) * 2.8f
                        rotationY = cos(lensOffset.x * 0.0028f) * 2.8f
                        alpha = 0.88f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0102f) * 3.8f
                            val distortionY = cos(lensOffset.x * 0.0102f) * 3.8f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(0.7.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(1.7.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.62f), Color.White.copy(alpha = 0.42f),
                        Color.Cyan.copy(alpha = 0.32f), Color.Blue.copy(alpha = 0.22f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.30f), Color.Transparent, Color.White.copy(alpha = 0.10f)),
                    center = Offset(0.31f, 0.31f), radius = lensRadiusPx * 0.82f))
        )
    }
}

@Composable
fun NewLensEffect209(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "209dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 8.7f
                        scaleY = 8.7f
                        rotationZ = sin(lensOffset.x * 0.0051f) * 1.9f
                        cameraDistance = 9.9f
                        rotationX = sin(lensOffset.y * 0.0029f) * 2.9f
                        rotationY = cos(lensOffset.x * 0.0029f) * 2.9f
                        alpha = 0.87f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0101f) * 3.9f
                            val distortionY = cos(lensOffset.x * 0.0101f) * 3.9f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(0.75.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(1.75.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.61f), Color.White.copy(alpha = 0.41f),
                        Color.Cyan.copy(alpha = 0.31f), Color.Blue.copy(alpha = 0.21f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.29f), Color.Transparent, Color.White.copy(alpha = 0.095f)),
                    center = Offset(0.305f, 0.305f), radius = lensRadiusPx * 0.81f))
        )
    }
}

@Composable
fun NewLensEffect208(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "208dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 8.6f
                        scaleY = 8.6f
                        rotationZ = sin(lensOffset.x * 0.0050f) * 2.0f
                        cameraDistance = 10.0f
                        rotationX = sin(lensOffset.y * 0.0030f) * 3.0f
                        rotationY = cos(lensOffset.x * 0.0030f) * 3.0f
                        alpha = 0.86f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0100f) * 4.0f
                            val distortionY = cos(lensOffset.x * 0.0100f) * 4.0f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(0.8.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(1.8.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.60f), Color.White.copy(alpha = 0.40f),
                        Color.Cyan.copy(alpha = 0.30f), Color.Blue.copy(alpha = 0.20f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.28f), Color.Transparent, Color.White.copy(alpha = 0.09f)),
                    center = Offset(0.32f, 0.32f), radius = lensRadiusPx * 0.80f))
        )
    }
}

@Composable
fun NewLensEffect207(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "207dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 8.5f
                        scaleY = 8.5f
                        rotationZ = sin(lensOffset.x * 0.0049f) * 2.1f
                        cameraDistance = 10.1f
                        rotationX = sin(lensOffset.y * 0.0031f) * 3.1f
                        rotationY = cos(lensOffset.x * 0.0031f) * 3.1f
                        alpha = 0.85f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0099f) * 4.1f
                            val distortionY = cos(lensOffset.x * 0.0099f) * 4.1f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(0.85.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(1.85.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.59f), Color.White.copy(alpha = 0.39f),
                        Color.Cyan.copy(alpha = 0.29f), Color.Blue.copy(alpha = 0.19f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.27f), Color.Transparent, Color.White.copy(alpha = 0.085f)),
                    center = Offset(0.315f, 0.315f), radius = lensRadiusPx * 0.79f))
        )
    }
}

@Composable
fun NewLensEffect206(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "206dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 8.4f
                        scaleY = 8.4f
                        rotationZ = sin(lensOffset.x * 0.0048f) * 2.2f
                        cameraDistance = 10.2f
                        rotationX = sin(lensOffset.y * 0.0032f) * 3.2f
                        rotationY = cos(lensOffset.x * 0.0032f) * 3.2f
                        alpha = 0.84f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0098f) * 4.2f
                            val distortionY = cos(lensOffset.x * 0.0098f) * 4.2f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(0.9.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(1.9.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.58f), Color.White.copy(alpha = 0.38f),
                        Color.Cyan.copy(alpha = 0.28f), Color.Blue.copy(alpha = 0.18f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.26f), Color.Transparent, Color.White.copy(alpha = 0.08f)),
                    center = Offset(0.31f, 0.31f), radius = lensRadiusPx * 0.78f))
        )
    }
}

@Composable
fun NewLensEffect205(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "205dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 8.3f
                        scaleY = 8.3f
                        rotationZ = sin(lensOffset.x * 0.0047f) * 2.3f
                        cameraDistance = 10.3f
                        rotationX = sin(lensOffset.y * 0.0033f) * 3.3f
                        rotationY = cos(lensOffset.x * 0.0033f) * 3.3f
                        alpha = 0.83f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0097f) * 4.3f
                            val distortionY = cos(lensOffset.x * 0.0097f) * 4.3f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(0.95.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(1.95.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.57f), Color.White.copy(alpha = 0.37f),
                        Color.Cyan.copy(alpha = 0.27f), Color.Blue.copy(alpha = 0.17f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.25f), Color.Transparent, Color.White.copy(alpha = 0.075f)),
                    center = Offset(0.325f, 0.325f), radius = lensRadiusPx * 0.77f))
        )
    }
}

@Composable
fun NewLensEffect204(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "204dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 8.2f
                        scaleY = 8.2f
                        rotationZ = sin(lensOffset.x * 0.0046f) * 2.4f
                        cameraDistance = 10.4f
                        rotationX = sin(lensOffset.y * 0.0034f) * 3.4f
                        rotationY = cos(lensOffset.x * 0.0034f) * 3.4f
                        alpha = 0.82f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0096f) * 4.4f
                            val distortionY = cos(lensOffset.x * 0.0096f) * 4.4f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.0.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(2.0.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.56f), Color.White.copy(alpha = 0.36f),
                        Color.Cyan.copy(alpha = 0.26f), Color.Blue.copy(alpha = 0.16f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.24f), Color.Transparent, Color.White.copy(alpha = 0.07f)),
                    center = Offset(0.32f, 0.32f), radius = lensRadiusPx * 0.76f))
        )
    }
}

@Composable
fun NewLensEffect203(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "203dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 8.1f
                        scaleY = 8.1f
                        rotationZ = sin(lensOffset.x * 0.0045f) * 2.5f
                        cameraDistance = 10.5f
                        rotationX = sin(lensOffset.y * 0.0035f) * 3.5f
                        rotationY = cos(lensOffset.x * 0.0035f) * 3.5f
                        alpha = 0.81f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0095f) * 4.5f
                            val distortionY = cos(lensOffset.x * 0.0095f) * 4.5f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.05.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(2.05.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.55f), Color.White.copy(alpha = 0.35f),
                        Color.Cyan.copy(alpha = 0.25f), Color.Blue.copy(alpha = 0.15f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.23f), Color.Transparent, Color.White.copy(alpha = 0.065f)),
                    center = Offset(0.315f, 0.315f), radius = lensRadiusPx * 0.75f))
        )
    }
}

@Composable
fun NewLensEffect202(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "202dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 8.0f
                        scaleY = 8.0f
                        rotationZ = sin(lensOffset.x * 0.0044f) * 2.6f
                        cameraDistance = 10.6f
                        rotationX = sin(lensOffset.y * 0.0036f) * 3.6f
                        rotationY = cos(lensOffset.x * 0.0036f) * 3.6f
                        alpha = 0.80f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0094f) * 4.6f
                            val distortionY = cos(lensOffset.x * 0.0094f) * 4.6f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.1.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(2.1.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.54f), Color.White.copy(alpha = 0.34f),
                        Color.Cyan.copy(alpha = 0.24f), Color.Blue.copy(alpha = 0.14f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.22f), Color.Transparent, Color.White.copy(alpha = 0.06f)),
                    center = Offset(0.31f, 0.31f), radius = lensRadiusPx * 0.74f))
        )
    }
}

@Composable
fun NewLensEffect201(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .size(lensRadius * 2)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "201dp透镜效果",
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = 7.9f
                        scaleY = 7.9f
                        rotationZ = sin(lensOffset.x * 0.0043f) * 2.7f
                        cameraDistance = 10.7f
                        rotationX = sin(lensOffset.y * 0.0037f) * 3.7f
                        rotationY = cos(lensOffset.x * 0.0037f) * 3.7f
                        alpha = 0.79f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()
                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth, maxWidth = screenWidth,
                                minHeight = screenHeight, maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            val distortionX = sin(lensOffset.y * 0.0093f) * 4.7f
                            val distortionY = cos(lensOffset.x * 0.0093f) * 4.7f
                            val offsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + distortionX.roundToInt()
                            val offsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + distortionY.roundToInt()
                            placeable.place(offsetX, offsetY)
                        }
                    }
                    .blur(1.15.dp),
                contentScale = ContentScale.Crop
            )
        }
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .border(2.15.dp, Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.53f), Color.White.copy(alpha = 0.33f),
                        Color.Cyan.copy(alpha = 0.23f), Color.Blue.copy(alpha = 0.13f), Color.Transparent),
                    radius = lensRadiusPx), CircleShape)
        )
        Box(
            modifier = Modifier.fillMaxSize().clip(CircleShape)
                .background(Brush.radialGradient(
                    colors = listOf(Color.White.copy(alpha = 0.21f), Color.Transparent, Color.White.copy(alpha = 0.055f)),
                    center = Offset(0.305f, 0.305f), radius = lensRadiusPx * 0.73f))
        )
    }
}

@Composable
fun MagnifierGlass(
    backgroundImageUri: Uri?
) {
    // 放大镜位置状态
    var magnifierOffset by remember { mutableStateOf(Offset(200f, 200f)) }

    // 获取屏幕尺寸
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidthPx = with(density) { configuration.screenWidthDp.dp.toPx() }
    val screenHeightPx = with(density) { configuration.screenHeightDp.dp.toPx() }

    // 最大放大镜大小（现在是220dp，缩小30dp）
    val baseMagnifierSize = 220.dp
    val baseMagnifierSizePx = with(density) { baseMagnifierSize.toPx() }

    Box(
        modifier = Modifier
            .size(baseMagnifierSize)
            .offset {
                IntOffset(
                    magnifierOffset.x.roundToInt() - (baseMagnifierSizePx / 2).roundToInt(),
                    magnifierOffset.y.roundToInt() - (baseMagnifierSizePx / 2).roundToInt()
                )
            }
            .pointerInput(Unit) {
                detectDragGestures { change, dragAmount ->
                    magnifierOffset += dragAmount
                    // 限制在屏幕范围内
                    magnifierOffset = Offset(
                        magnifierOffset.x.coerceIn(baseMagnifierSizePx / 2, screenWidthPx - baseMagnifierSizePx / 2),
                        magnifierOffset.y.coerceIn(baseMagnifierSizePx / 2, screenHeightPx - baseMagnifierSizePx / 2)
                    )
                }
            }
            .clip(CircleShape) // 整体圆形裁剪
    ) {
        // 最外层新增第一圈 - 220dp，13倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 220.dp,
                magnificationFactor = 13f
            )
        }

        // 最外层新增第二圈 - 210dp，12倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 210.dp,
                magnificationFactor = 12f
            )
        }

        // 最外层新增第三圈 - 200dp，11倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 200.dp,
                magnificationFactor = 11f
            )
        }

        // 最外层新增第四圈 - 190dp，10倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 190.dp,
                magnificationFactor = 10f
            )
        }

        // 最外层新增第五圈 - 180dp，9倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 180.dp,
                magnificationFactor = 9f
            )
        }

        // 原最外层第一圈 - 170dp，8倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 170.dp,
                magnificationFactor = 8f
            )
        }

        // 最外层第二圈 - 160dp，7倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 160.dp,
                magnificationFactor = 7f
            )
        }

        // 最外层第三圈 - 150dp，6倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 150.dp,
                magnificationFactor = 6f
            )
        }

        // 原第一层放大镜 - 140dp，5倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 140.dp,
                magnificationFactor = 5f
            )
        }

        // 第二层放大镜 - 130dp，4倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 130.dp,
                magnificationFactor = 4f
            )
        }

        // 第三层放大镜 - 120dp，3倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 120.dp,
                magnificationFactor = 3f
            )
        }

        // 第四层放大镜 - 110dp，2倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 110.dp,
                magnificationFactor = 2f
            )
        }

        // 第五层放大镜 - 100dp，1倍放大
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 100.dp,
                magnificationFactor = 1f
            )
        }

        // 原最内层 - 90dp，0.8倍缩小
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 90.dp,
                magnificationFactor = 0.8f
            )
        }

        // 新增内圈第一层 - 80dp，0.7倍缩小
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 80.dp,
                magnificationFactor = 0.7f
            )
        }

        // 新增内圈第二层 - 70dp，0.6倍缩小
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 70.dp,
                magnificationFactor = 0.6f
            )
        }

        // 新增内圈第三层 - 60dp，0.5倍缩小
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentAlignment = Alignment.Center
        ) {
            MagnifierLayer(
                backgroundImageUri = backgroundImageUri,
                magnifierOffset = magnifierOffset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                magnifierSize = 60.dp,
                magnificationFactor = 0.5f
            )
        }
    }
}

@Composable
fun MagnifierLayer(
    backgroundImageUri: Uri?,
    magnifierOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    magnifierSize: androidx.compose.ui.unit.Dp,
    magnificationFactor: Float
) {
    val density = LocalDensity.current
    val magnifierSizePx = with(density) { magnifierSize.toPx() }

    // 根据圆圈大小计算透明度，越小的圆圈透明度越高
    val alpha = when {
        magnifierSizePx >= 200f -> 0.95f  // 最外层圆圈
        magnifierSizePx >= 150f -> 0.85f  // 中外层圆圈
        magnifierSizePx >= 100f -> 0.75f  // 中间层圆圈
        magnifierSizePx >= 70f -> 0.65f   // 中内层圆圈
        else -> 0.55f                     // 最内层圆圈
    }

    // 根据圆圈大小计算模糊程度
    val blurRadius = when {
        magnifierSizePx >= 200f -> 0.dp   // 最外层不模糊
        magnifierSizePx >= 150f -> 1.dp   // 轻微模糊
        magnifierSizePx >= 100f -> 2.dp   // 中等模糊
        magnifierSizePx >= 70f -> 3.dp    // 较强模糊
        else -> 4.dp                      // 最强模糊
    }

    Box(
        modifier = Modifier
            .size(magnifierSize)
            .clip(CircleShape)
    ) {
        // 放大的背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "放大镜视图 ${magnificationFactor}x",
                modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape)
                    .blur(blurRadius) // 添加模糊效果
                    .graphicsLayer {
                        // 指定倍数放大
                        scaleX = magnificationFactor
                        scaleY = magnificationFactor
                        // 渐变透明度
                        this.alpha = alpha
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )

                        layout(constraints.maxWidth, constraints.maxHeight) {
                            // 计算偏移，使放大镜中心对应的背景区域显示在放大镜中
                            val offsetX = -magnifierOffset.x.roundToInt() + (constraints.maxWidth / 2)
                            val offsetY = -magnifierOffset.y.roundToInt() + (constraints.maxHeight / 2)

                            placeable.place(offsetX, offsetY)
                        }
                    },
                contentScale = ContentScale.Crop
            )
        }

        // 添加边缘渐变效果，消除硬边界
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.Transparent,
                            Color.Transparent,
                            Color.White.copy(alpha = 0.1f),
                            Color.White.copy(alpha = 0.2f)
                        ),
                        radius = magnifierSizePx * 0.8f
                    )
                )
        )
    }
}